# ميزة القوائم المنسدلة للمتغيرات

## نظرة عامة
تم إضافة ميزة جديدة تسمح بتحويل متغيرات المنتج (الألوان، الأحجام، إلخ) من الصناديق التفاعلية إلى قوائم منسدلة تقليدية. هذه الميزة مفيدة بشكل خاص للمنتجات التي تحتوي على عدد كبير من المتغيرات.

## كيفية التفعيل

### 1. الوصول إلى الإعدادات
1. اذهب إلى لوحة تحكم WordPress
2. انتقل إلى **WooCommerce > إعدادات الدفع عند الاستلام**
3. اختر تبويبة **"إعدادات النموذج"**

### 2. تفعيل الميزة
1. ابحث عن خيار **"تحويل متغيرات المنتج إلى قوائم منسدلة"**
2. قم بتفعيل الخيار
3. احفظ الإعدادات

## الميزات

### ✅ المزايا
- **سهولة الاستخدام**: قوائم منسدلة مألوفة للمستخدمين
- **توفير المساحة**: مناسبة للمنتجات ذات المتغيرات الكثيرة
- **إمكانية الوصول**: دعم أفضل لقارئات الشاشة
- **التوافق**: يعمل مع جميع أنواع المتغيرات (ألوان، أحجام، إلخ)
- **الخيارات الذكية**: إظهار الخيارات المتاحة فقط حسب الاختيارات السابقة
- **التفاعل المتقدم**: تعطيل الخيارات غير المتاحة مع إشارات بصرية واضحة

### 🎨 التصميم
- **تنسيق متسق**: يتبع نفس تصميم النموذج
- **دعم RTL**: يعمل بشكل مثالي مع اللغة العربية
- **استجابة**: متوافق مع جميع أحجام الشاشات
- **نموذج حديث**: تنسيق خاص للنموذج الحديث

## الميزات المتقدمة

### 🧠 الخيارات الذكية
- **التفاعل التلقائي**: عند اختيار لون معين، تظهر فقط الأحجام المتاحة لذلك اللون
- **التعطيل البصري**: الخيارات غير المتاحة تظهر بلون رمادي مع نص "(غير متاح)"
- **عداد الخيارات**: يظهر عدد الخيارات المتاحة في النص الافتراضي للقائمة
- **إعادة التعيين الذكية**: عند تغيير الاختيار الأول، يتم مسح الاختيارات التالية تلقائياً

### 📱 مثال عملي
```
اللون: أحمر → الأحجام المتاحة: صغير، متوسط، كبير
اللون: أزرق → الأحجام المتاحة: متوسط، كبير فقط
اللون: أخضر → الحجم المتاح: كبير فقط
```

## التفاصيل التقنية

### الملفات المعدلة
1. **includes/class-rid-cod-customizer.php**
   - إضافة إعداد جديد: `rid_cod_use_dropdown_variations`
   - تسجيل الإعداد في قاعدة البيانات

2. **includes/class-rid-cod-form.php**
   - تعديل كود عرض المتغيرات
   - إضافة منطق إظهار/إخفاء القوائم والصناديق
   - تمرير الإعداد إلى JavaScript

3. **assets/css/rid-cod.css**
   - إضافة تنسيقات للقوائم المنسدلة
   - دعم النموذج الحديث والكلاسيكي
   - تنسيقات RTL والشاشات الصغيرة
   - تنسيقات للخيارات المعطلة

4. **assets/js/rid-cod.js**
   - إضافة منطق إدارة وضع القوائم المنسدلة
   - إضافة class للـ body عند التفعيل

5. **assets/js/rid-cod-variations.js**
   - دعم وضع القوائم المنسدلة
   - إضافة دالة `updateDropdownOptions` للتحكم في الخيارات المتاحة
   - معالجة إعادة التعيين والتحديث التلقائي

### CSS Classes المضافة
- `.rid-cod-use-dropdown-variations`: يُضاف للـ body عند تفعيل الوضع
- تنسيقات خاصة للقوائم المنسدلة في كلا النموذجين

### JavaScript Variables
- `rid_cod_params.use_dropdown_variations`: متغير يحدد حالة التفعيل

### الوظائف الجديدة المضافة
- `updateDropdownOptions($select, availableValues, isFirstAttribute)`: تحديث خيارات القائمة المنسدلة
- معالجة تلقائية للخيارات المعطلة عند إعادة التعيين
- إضافة عداد للخيارات المتاحة في النص الافتراضي

## الاستخدام

### الوضع الافتراضي (صناديق تفاعلية)
```html
<div class="variation-boxes">
    <div class="variation-option">خيار 1</div>
    <div class="variation-option">خيار 2</div>
</div>
```

### وضع القوائم المنسدلة
```html
<select class="rid-cod-variation-select" style="display: block;">
    <option value="">اختر الخيار</option>
    <option value="option1">خيار 1</option>
    <option value="option2">خيار 2</option>
</select>
```

## التوافق
- ✅ WordPress 5.0+
- ✅ WooCommerce 4.0+
- ✅ جميع المتصفحات الحديثة
- ✅ الأجهزة المحمولة
- ✅ قارئات الشاشة

## الاختبار
تم إنشاء ملف اختبار: `test-dropdown-variations.html` لاختبار الميزة بشكل مستقل.

## ملاحظات للمطورين
- الميزة تحافظ على التوافق مع الكود الموجود
- لا تؤثر على وظائف WooCommerce الأساسية
- يمكن تبديل الوضع في أي وقت دون فقدان البيانات
- تدعم جميع أنواع المتغيرات (ألوان، نصوص، أرقام)

## الدعم
في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع فريق الدعم.
