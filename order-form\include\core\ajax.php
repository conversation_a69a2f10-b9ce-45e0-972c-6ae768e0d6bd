<?php

defined("ABSPATH") or die("You cannot access it in this way .");

add_action( 'wp_ajax_get_shipping_methods', 'get_shipping_methods' );
add_action( 'wp_ajax_nopriv_get_shipping_methods', 'get_shipping_methods' );

function get_shipping_methods() {
    if ( isset ($_POST["action"] )  ) {
        $state = $_POST['state'];
        
        // Retrieve the shipping zones that match the selected state
        $delivery_zones = array_filter( WC_Shipping_Zones::get_zones(), function( $zone ) use ( $state ) {
            return stripos( $zone["formatted_zone_location"], $state ) !== false;
        } );
        
        $output ='';
        
        foreach ((array) $delivery_zones as $key => $zone) {
         if (count($zone["shipping_methods"]) > 1 ) {
           foreach ($zone["shipping_methods"] as $method) {
            $output .= '<input class="radio-button-color" type="radio" name="shipping_method" value="' . esc_attr( $method->get_title() ) . '"style="height: 35px; width: 30px; vertical-align: middle; margin-bottom: 0px;" > ' . esc_html( $method->get_title() ) . '<br>';
        }
    }
}
        
        echo $output;
        die();
    }
}


add_action(
    "wp_ajax_codplugin_woo_order_action",
    "codplugin_woo_order_ajax_function"
);

add_action(
    "wp_ajax_nopriv_codplugin_woo_order_action",
    "codplugin_woo_order_ajax_function"
);

function codplugin_woo_order_ajax_function() {
    if (isset($_POST["action"]) && isset($_POST["value"])) {
       
        global $woocommerce;

        $state = $_POST["value"];
        
        if ( isset($_POST["variation_id"] ) ) {
            $variation_id = $_POST["variation_id"];
            $_product = wc_get_product( $variation_id );
            $product_class_id = $_product->get_shipping_class_id();
        }  elseif ( isset($_POST["product_id"] ) ) {
            $product_id = $_POST["product_id"];
            $_product = wc_get_product( $product_id );
            $product_class_id = $_product->get_shipping_class_id();
        }

        if ( isset($_POST["d_method"] ) ) {
            $d_method = $_POST["d_method"];
        }

        $delivery_zones = array_filter( WC_Shipping_Zones::get_zones(), function( $zone ) use ( $state ) {
            return stripos( $zone["formatted_zone_location"], $state ) !== false;
        } );

        $cost = 0; // Initialize price to 0

        foreach ((array) $delivery_zones as $key => $the_zone) {

                if (count($the_zone["shipping_methods"]) > 1 && isset($_POST["d_method"]) ) {
                    foreach ($the_zone["shipping_methods"] as $value) {
                        
                       // if (in_array($value->id, ['flat_rate', 'local_pickup'])) {
                        if (strpos($value->id, 'flat_rate') !== false  || strpos($value->id, 'local_pickup') !== false ) {
                            $data = $value->instance_settings;
                            if ( $d_method ===  $data['title']  ) {

                                // For when shipping class is defined

                                if( isset($product_class_id) && ! empty($product_class_id) 
                                && isset($data['class_cost_'.$product_class_id]) )  {
                                    $cost = $data['class_cost_'.$product_class_id];
                                } 
                                // For no defined shipping class when "no class cost" is defined
                                elseif( isset($product_class_id) && empty($product_class_id) 
                                && isset($data['no_class_cost']) && $data['no_class_cost'] > 0 ) {
                                    $cost = $data['no_class_cost'];

                                }
                               
                                else {
                                    $cost = $data['cost'];
                                }

                                break 2;

                            }

                        }
                    }
                } else {

                    foreach ($the_zone["shipping_methods"] as $value) {
                        
                      // if (in_array($value->id, ['flat_rate', 'local_pickup'])) {
                        if (strpos($value->id, 'flat_rate') !== false  || strpos($value->id, 'local_pickup') !== false ) {
                                $data = $value->instance_settings;

                                // For when shipping class is defined

                                if( isset($product_class_id) && ! empty($product_class_id) 
                                && isset($data['class_cost_'.$product_class_id]) )  {
                                    $cost = $data['class_cost_'.$product_class_id];
                                } 
                                // For no defined shipping class when "no class cost" is defined
                                elseif( isset($product_class_id) && empty($product_class_id) 
                                && isset($data['no_class_cost']) && $data['no_class_cost'] > 0 ) {
                                    $cost = $data['no_class_cost'];

                                }
                               
                                else {
                                    $cost = $data['cost'];
                                }

                                break 2;
                            
                        }
                    }

                }
        }
        echo $cost;
        die();
    }
}

// Create new orders with ajax
if ( get_theme_mod( 'create_orders_with_php', 0)  == 0 ) :

    add_action(
        "wp_ajax_codplugin_order_form_action",
        "codplugin_order_form_action_function"
    );

    add_action(
        "wp_ajax_nopriv_codplugin_order_form_action",
        "codplugin_order_form_action_function"
    );

    function codplugin_order_form_action_function()
    {
        
        if (
            isset($_POST["action"]) &&
            isset($_POST["full_name"]) &&
            isset($_POST["phone_number"]) &&
            isset($_POST["full_address"]) &&
            isset($_POST["codplugin_state"]) &&
            isset($_POST["codplugin_c_number"]) &&
            isset($_POST["codplugin_price"])
        ) {

            global $woocommerce;
            $countries_obj = new WC_Countries();
            $countries = $countries_obj->__get("countries");
            $default_country = $countries_obj->get_base_country();

            $product_id = $_POST["product_id"];
            $full_name = $_POST["full_name"];
            $phone_number = $_POST["phone_number"];
            $full_address = $_POST["full_address"];
            $codplugin_state = $_POST["codplugin_state"];
            $codplugin_city = isset($_POST["codplugin_city"]) ? $_POST["codplugin_city"] : '';
            $count_number = $_POST["codplugin_c_number"];
            $product_price = $_POST["codplugin_price"];
            $d_price = $_POST["d_price"];
            $codplugin_d_method = $_POST["codplugin_d_method"];
            $total_price = (int)$count_number * (int)$product_price + (int)$d_price;
            if (isset($_POST["codplugin_email"])) {
                $codplugin_email = $_POST["codplugin_email"];
            }


            if ($default_country == "DZ" ) {
                $states_dz = array( 'Adrar' => '01 Adrar - أدرار', 'Chlef' => '02 Chlef - الشلف', 'Laghouat' => '03 Laghouat - الأغواط', 'Oum El Bouaghi' => '04 Oum El Bouaghi - أم البواقي', 'Batna' => '05 Batna - باتنة', 'Béjaïa' => '06 Béjaïa - بجاية', 'Biskra' => '07 Biskra - بسكرة', 'Bechar' => '08 Bechar - بشار', 'Blida' => '09 Blida - البليدة', 'Bouira' => '10 Bouira - البويرة', 'Tamanrasset' => '11 Tamanrasset - تمنراست ', 'Tébessa' => '12 Tébessa - تبسة ', 'Tlemcene' => '13 Tlemcene - تلمسان', 'Tiaret' => '14 Tiaret - تيارت', 'Tizi Ouzou' => '15 Tizi Ouzou - تيزي وزو', 'Alger' => '16 Alger - الجزائر', 'Djelfa' => '17 Djelfa - الجلفة', 'Jijel' => '18 Jijel - جيجل', 'Sétif' => '19 Sétif - سطيف', 'Saïda' => '20 Saïda - سعيدة', 'Skikda' => '21 Skikda - سكيكدة', 'Sidi Bel Abbès' => '22 Sidi Bel Abbès - سيدي بلعباس', 'Annaba' => '23 Annaba - عنابة', 'Guelma' => '24 Guelma - قالمة', 'Constantine' => '25 Constantine - قسنطينة', 'Médéa' => '26 Médéa - المدية', 'Mostaganem' => '27 Mostaganem - مستغانم', 'MSila' => '28 MSila - مسيلة', 'Mascara' => '29 Mascara - معسكر', 'Ouargla' => '30 Ouargla - ورقلة', 'Oran' => '31 Oran - وهران', 'El Bayadh' => '32 El Bayadh - البيض', 'Illizi' => '33 Illizi - إليزي ', 'Bordj Bou Arreridj' => '34 Bordj Bou Arreridj - برج بوعريريج', 'Boumerdès' => '35 Boumerdès - بومرداس', 'El Tarf' => '36 El Tarf - الطارف', 'Tindouf' => '37 Tindouf - تندوف', 'Tissemsilt' => '38 Tissemsilt - تيسمسيلت', 'Eloued' => '39 Eloued - الوادي', 'Khenchela' => '40 Khenchela - خنشلة', 'Souk Ahras' => '41 Souk Ahras - سوق أهراس', 'Tipaza' => '42 Tipaza - تيبازة', 'Mila' => '43 Mila - ميلة', 'Aïn Defla' => '44 Aïn Defla - عين الدفلى', 'Naâma' => '45 Naâma - النعامة', 'Aïn Témouchent' => '46 Aïn Témouchent - عين تموشنت', 'Ghardaïa' => '47 Ghardaïa - غرداية', 'Relizane' => '48 Relizane- غليزان', 'Timimoun' => '49 Timimoun - تيميمون', 'Bordj Baji Mokhtar' => '50 Bordj Baji Mokhtar - برج باجي مختار', 'Ouled Djellal' => '51 Ouled Djellal - أولاد جلال', 'Béni Abbès' => '52 Béni Abbès - بني عباس', 'Aïn Salah' => '53 Aïn Salah - عين صالح', 'In Guezzam' => '54 In Guezzam - عين قزام', 'Touggourt' => '55 Touggourt - تقرت', 'Djanet' => '56 Djanet - جانت', 'El MGhair' => '57 El MGhair - المغير', 'El Menia' => '58 El Menia - المنيعة', );

                $codplugin_state = array_search ($codplugin_state, $states_dz);
            }
        
            $address = [
                "first_name" => $full_name,
                "phone" => $phone_number,
                "address_1" => $full_address,
                "state" => $codplugin_state,
                "city" => $codplugin_city,
                "email" => isset($codplugin_email) ? $codplugin_email : null,
            ];


            $order = wc_create_order();

            if(isset($_POST["var_id"])){
                $order->add_product(wc_get_product($_POST["var_id"]), $count_number);
            }else{
                $order->add_product(wc_get_product($product_id), $count_number);
            }

            // add shipping
            $shipping = new WC_Order_Item_Shipping();

            $codplugin_d_method_free = '';

            if ($_POST["d_price"] == 0 ) { 
                $codplugin_d_method_free = __('FREE', 'napoleon') ;
            }
            
            $shipping->set_method_title( $codplugin_d_method . ' ' . $codplugin_d_method_free );
            
            $shipping->set_total( $d_price ); 
            $order->add_item( $shipping );

            
            $order->set_address($address, "billing");
            $order->set_address($address, "shipping");

            $order->set_payment_method('cod');


            $order->calculate_totals();
            $order->save();


            $order_id=$order->get_id();
            $order_key = $order->get_order_key();            
            $nr_array=array("order_id"=>$order_id,"order_key"=>$order_key);
            echo json_encode($nr_array);

            $order->update_status('processing');

            die();
        } else {
            die();
        }

    }



    add_action(
        "wp_ajax_codplugin_add_upsell_product",
        "codplugin_add_upsell_product"
    );

    add_action(
        "wp_ajax_nopriv_codplugin_add_upsell_product",
        "codplugin_add_upsell_product"
    );


    function codplugin_add_upsell_product(){
        if (isset($_POST["order_id"]) && isset($_POST["product_id"])){
            $order = wc_get_order( $_POST["order_id"] );
            $order->add_product(wc_get_product($_POST["product_id"]), 1);
                $order->calculate_totals();

            $return = array(
                'message' => __( 'Saved', 'napoleon' ),
                'ID'      => 1
            );
            wp_send_json_success( $return ); 
            // die();
        }else{
            wp_send_json_error();
        }
    }

endif; 