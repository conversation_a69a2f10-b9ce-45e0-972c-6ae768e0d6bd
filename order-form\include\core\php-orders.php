<?php



if ( get_theme_mod( 'create_orders_with_php', 0)  == 0 ) {
    return;
}

/**
 *  Create new orders with php
 */

add_action('init', 'napoleon_order_form_submission');

// Create new orders through php
function napoleon_order_form_submission() {
    global $woocommerce;
  
    $countries_obj = new WC_Countries();
    $countries = $countries_obj->__get("countries");
    $default_country = $countries_obj->get_base_country();

  //  if (isset($_POST['codplugin-submit'])) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {

        // Verify the nonce field to prevent CSRF attacks
        if (isset($_POST['rwc-form-nonce']) ) {
            // Get the form data

            $product_id = $_POST["product_id"];
            $full_name = $_POST["full_name"];
            $phone_number = $_POST["phone_number"];
            $full_address = $_POST["full_address"];
            $codplugin_state = $_POST["codplugin_state"];
            $codplugin_city = isset($_POST["codplugin_city"]) ? $_POST["codplugin_city"] : '';
            $count_number = $_POST["codplugin_c_number"];
            $product_price = $_POST["codplugin_price"];
            $d_price = $_POST["d_price"];
            $codplugin_d_method = $_POST["codplugin_d_method"];
            $total_price = (int)$count_number * (int)$product_price + (int)$d_price;

            if (isset($_POST["codplugin_email"])) {
                $codplugin_email = $_POST["codplugin_email"];
            }


            if ( get_theme_mod( 'enable_latin_state_name', 0)  == 1 ) {
                if ($default_country == "DZ" ) {
                    $states_dz = array(
                        'DZ-01' => '01 Adrar - أدرار',
                        'DZ-02' => '02 Chlef - الشلف',
                        'DZ-03' => '03 Laghouat - الأغواط',
                        'DZ-04' => '04 Oum El Bouaghi - أم البواقي',
                        'DZ-05' => '05 Batna - باتنة',
                        'DZ-06' => '06 Béjaïa - بجاية',
                        'DZ-07' => '07 Biskra - بسكرة',
                        'DZ-08' => '08 Bechar - بشار',
                        'DZ-09' => '09 Blida - البليدة',
                        'DZ-10' => '10 Bouira - البويرة',
                        'DZ-11' => '11 Tamanrasset - تمنراست ',
                        'DZ-12' => '12 Tébessa - تبسة ',
                        'DZ-13' => '13 Tlemcene - تلمسان',
                        'DZ-14' => '14 Tiaret - تيارت',
                        'DZ-15' => '15 Tizi Ouzou - تيزي وزو',
                        'DZ-16' => '16 Alger - الجزائر',
                        'DZ-17' => '17 Djelfa - الجلفة',
                        'DZ-18' => '18 Jijel - جيجل',
                        'DZ-19' => '19 Sétif - سطيف',
                        'DZ-20' => '20 Saïda - سعيدة',
                        'DZ-21' => '21 Skikda - سكيكدة',
                        'DZ-22' => '22 Sidi Bel Abbès - سيدي بلعباس',
                        'DZ-23' => '23 Annaba - عنابة',
                        'DZ-24' => '24 Guelma - قالمة',
                        'DZ-25' => '25 Constantine - قسنطينة',
                        'DZ-26' => '26 Médéa - المدية',
                        'DZ-27' => '27 Mostaganem - مستغانم',
                        'DZ-28' => '28 MSila - مسيلة',
                        'DZ-29' => '29 Mascara - معسكر',
                        'DZ-30' => '30 Ouargla - ورقلة',
                        'DZ-31' => '31 Oran - وهران',
                        'DZ-32' => '32 El Bayadh - البيض',
                        'DZ-33' => '33 Illizi - إليزي ',
                        'DZ-34' => '34 Bordj Bou Arreridj - برج بوعريريج',
                        'DZ-35' => '35 Boumerdès - بومرداس',
                        'DZ-36' => '36 El Tarf - الطارف',
                        'DZ-37' => '37 Tindouf - تندوف',
                        'DZ-38' => '38 Tissemsilt - تيسمسيلت',
                        'DZ-39' => '39 Eloued - الوادي',
                        'DZ-40' => '40 Khenchela - خنشلة',
                        'DZ-41' => '41 Souk Ahras - سوق أهراس',
                        'DZ-42' => '42 Tipaza - تيبازة',
                        'DZ-43' => '43 Mila - ميلة',
                        'DZ-44' => '44 Aïn Defla - عين الدفلى',
                        'DZ-45' => '45 Naâma - النعامة',
                        'DZ-46' => '46 Aïn Témouchent - عين تموشنت',
                        'DZ-47' => '47 Ghardaïa - غرداية',
                        'DZ-48' => '48 Relizane- غليزان',
                        'DZ-49' => '49 Timimoun - تيميمون',
                        'DZ-50' => '50 Bordj Baji Mokhtar - برج باجي مختار',
                        'DZ-51' => '51 Ouled Djellal - أولاد جلال',
                        'DZ-52' => '52 Béni Abbès - بني عباس',
                        'DZ-53' => '53 Aïn Salah - عين صالح',
                        'DZ-54' => '54 In Guezzam - عين قزام',
                        'DZ-55' => '55 Touggourt - تقرت',
                        'DZ-56' => '56 Djanet - جانت',
                        'DZ-57' => '57 El MGhair - المغير',
                        'DZ-58' => '58 El Menia - المنيعة');

                    $codplugin_state = array_search ($codplugin_state, $states_dz);
                }
            }
                   
             $address = [
                "first_name" => $full_name,
                "phone" => $phone_number,
                "address_1" => $full_address,
                "state" => $codplugin_state,
                "city" => $codplugin_city,
                "email" => isset($codplugin_email) ? $codplugin_email : null,

            ];

            // Create a new WooCommerce order
            $order = wc_create_order();
            

            if ($order) {

                if(isset($_POST["var_id"])){
                    $order->add_product(wc_get_product($_POST["var_id"]), $count_number);
                } else {
                    $order->add_product(wc_get_product($product_id), $count_number);
                }

                // add shipping
                $shipping = new WC_Order_Item_Shipping();

                $codplugin_d_method_free = '';

                if ($_POST["d_price"] == 0 ) { 
                    $codplugin_d_method_free = __('FREE', 'napoleon') ;
                }
                
                $shipping->set_method_title( $codplugin_d_method . ' ' . $codplugin_d_method_free );
                
                $shipping->set_total( $d_price ); 
                $order->add_item( $shipping );
            

                // Update shipping address
               
                $order->set_address($address, "billing");
                $order->set_address($address, "shipping");


                $order->set_payment_method('cod');

                $order->calculate_totals();
                $order->save();

                $order->update_status('processing');
				
				 // Order Note
		        if (isset($_POST['order_notes']) && !empty($_POST['order_notes'])) {
                $order->set_customer_note($_POST['order_notes']);
                $order->save();
                }
           
                // Redirect to thank you page
                $order_received_url = wc_get_endpoint_url( 'order-received', $order->get_id(), wc_get_checkout_url() );
                $order_received_url = add_query_arg( 'key', $order->get_order_key(), $order_received_url );
                $order_received_url  = apply_filters( 'woocommerce_get_checkout_order_received_url', $order_received_url, $order );

                wp_redirect($order_received_url);

            }
        
        } 
    }
}




/**
 *  Display One click upsell in thank you page
 */
function add_upsells_to_order() {

    if (isset($_POST["order_id"]) && isset($_POST["product_id"])) {
         $order = wc_get_order( $_POST["order_id"] );
         $order->add_product(wc_get_product($_POST["product_id"]), 1);
         $order->calculate_totals();
    }
    wp_die();
}

add_action('wp_ajax_add_upsells_to_order', 'add_upsells_to_order');
add_action('wp_ajax_nopriv_add_upsells_to_order', 'add_upsells_to_order');



function order_upsells_popup() {
    
    if (get_theme_mod('show_upsells', 0) == 1 && is_order_received_page() ) {

        $order_id = get_query_var('order-received');
        $order = wc_get_order($order_id);

        if ($order) {
            // Loop through order items
            foreach ($order->get_items() as $item_id => $item) {
                // Get the product ID
                $product_id = $item->get_product_id();

                // Get the product object
                $product = wc_get_product($product_id);

                // Check if the product has upsells
                $upsell_ids = $product->get_upsell_ids();

                if (!empty($upsell_ids)) {
                    break;
                }
            }

            if (  !empty($upsell_ids) )  {   
                $upsell_id = $upsell_ids[0]; ?>
                <div id="cod-upsell">
                    <div id="cod-upsell-box">
                        <h2 class="cod-upsell-heading">
                            <?php if( get_theme_mod('upsell_title') ){
                                echo esc_html( get_theme_mod( 'upsell_title' ) );
                            } else {
                                echo esc_html__( 'Wait! Your order is not completed!', 'napoleon' );
                            } ?>
                        </h2>
                        <div class="cod-upsell-product">
                            <div class="cod-upsell-product-title">
                            <?php $upsell = wc_get_product($upsell_id);
                                echo $upsell->get_title(); ?>
                            </div>
                            <img  src="<?php echo get_the_post_thumbnail_url($upsell_id); ?>" class="img-responsive" alt=""/>
                            <span class="price">
                                <?php echo $upsell->get_price_html(); ?>
                            </span> 
                            <div id="upsell-submit">
                                <input type="hidden" id="upsell_product_id" name="upsell_product_id" value="<?php echo $upsell_id; ?>">
                                 <input type="hidden" id="current_order_id" name="current_order_id" value="<?php echo $order_id; ?>">
                                <button id="cod-add-upsell"><?php _e('Add To Cart', 'napoleon'); ?></button>
                                <button id="cod-upsell-cancel"><?php _e('No, Thanks', 'napoleon'); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
                <style>
                    #upsell-submit button,.cod-upsell-product img{display:block;margin:20px auto}#cod-upsell{display:none;position:fixed;z-index:25;left:0;top:0;width:100%;height:100%;overflow:auto;background-color:rgba(0,0,0,.5)}@keyframes dropDown{from{transform:translateY(-100%);opacity:0}to{transform:translateY(0);opacity:1}}#cod-upsell-box{background-color:#fff;margin:5% auto;padding:20px;border:1px solid #888;width:90%;animation:.5s ease-in-out dropDown}#cod-upsell-box .cod-upsell-heading{color:red;text-align:center;font-weight:700;font-size:45px;margin:0 0 20px;padding-bottom:20px;border-bottom:3px dotted #eee}.cod-upsell-product-title{text-align:center;font-size:24px;color:#000}.cod-upsell-product .price{display:block;text-align:center;color:red;margin-bottom:25px}#upsell-submit button{color:#fff;background-color:#4caf50;padding:15px 50px;font-size:30px;border:none}#upsell-submit #cod-upsell-cancel{background-color:transparent;color:#bababa;padding:0;font-size:20px}#upsell-submit button:hover{cursor:pointer}#upsell-submit #cod-add-upsell:hover{background-color:#222}#upsell-submit #cod-upsell-cancel:hover{color:#555}

                </style>

                <script>
                    jQuery(document).ready(function($) {

                        var popupShown = localStorage.getItem('popupShown');
                        
                        if (!popupShown) {
                            $('#cod-upsell').fadeIn();
                            localStorage.setItem('popupShown', true);
                        }
                        
                        $("#cod-upsell-cancel").click(function() {
                            $("#cod-upsell").fadeOut();
                        });
                        $('#cod-add-upsell').on('click', function() {
                           productID = $("#upsell_product_id").val();
                           orderID = $("#current_order_id").val();

                          $.ajax({
                            type: 'POST',
                             url: "<?php echo admin_url('admin-ajax.php'); ?>",
                            data: {
                              action: 'add_upsells_to_order',
                              order_id : orderID,
                              product_id: productID,
                            },
                            success: function(response) {
                              location.reload();
                            },
                            error: function(error) {
                              // Handle the error response
                              console.error(error);
                            }
                          });
                        });
                    });
                </script>

        <?php }
        } 
    }
}
add_action('wp_footer', 'order_upsells_popup');


